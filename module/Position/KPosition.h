﻿#pragma once

#include "KModuleDefine.h"

// 模块，每一种操作代表一种模块，可以是具体的算法，io操作，分支流程等

#pragma region 模板匹配模块
class KMatchModule : public KModule
{
public:
    KMatchModule();
    int run() override;

    cv::Mat ImageRotate(cv::Mat input, double angle);
    void drawTransformedRect(cv::Mat &image, const cv::Rect &rect, double angle);

    int MatchTemplate(cv::Mat input, cv::Mat temp, int matchMode, double &maxVal, cv::Point &maxLoc);
    int RotateMatch(cv::Mat input, cv::Mat temp, int matchMode, double &maxval, cv::Point &maxloc, double &bestangle);

private:
    /*输入参数*/
    KImage2Data _image2In;     // 输入图像
    cv::Mat _tempimg;          // 模板图
    KInt _downSampleNum = 0;   // 下采样次数
    KBool _rotateEnable = 1;   // 旋转匹配使能
    KInt _matchMethod = 5;     // 1-与opencv参数一致
    KDouble _angleStep = 10;   // 模板的匹配角度
    KDouble _minAngle = -45.0; // 角度范围最小值
    KDouble _maxAngle = 45.0;  // 角度范围最大值

    /*输出参数*/
    KImage2Data _image2Out; // 结果图
    KDouble _maxMatchscore; // 匹配分数
    KDouble _bestAngle;     // 最佳角度 - 在旋转匹配时有效
    cv::Point _location;    // 匹配位置
};

#pragma endregion

#pragma region 位置修正模块
class PositionCorrectKModule : public KModule
{
public:
    PositionCorrectKModule();
    int run() override;

private:
    // 输入
    KRect1DData _kRectsIn;  // 多目标时使用
    KRectData _finalRectIn; // 最大区域

    // 输出
    KRect1DData _kRectsOut;  // 多目标时使用
    KRectData _finalRectOut; // 最大区域

    // 设置
    KBool _enableMaxRect; // 修正最大定位框           //切图定位时使用
    KBool _enableAllRect; // 修正所有定位框           //多目标时使用
    KInt _leftTopX;       // 左上角x坐标
    KInt _leftTopY;       // 左上角y坐标
    KDouble _xScale;      // x方向伸缩尺度
    KDouble _yScale;      // y方向伸缩尺度
};

#pragma endregion

#pragma region Blob分析模块
class KBlobModule : public KModule
{
public:
    KBlobModule();
    int run() override;

private:
    // 输入
    KImage2Data _image2;          // 输入图像                     //rgb图像
    KImage2DataVec _binaryImages; // 分割结果二值图
    KRectData _finalRectIn;       // 分析区域
    KInt _channelNum;             // 分割算子输出类别数            //需根据模型信息设置合理的参数范围

    // 输出
    KContours _contours;     // 轮廓信息
    KRect1DData _kRects;     // 多目标时使用
    KRectData _finalRectOut; // 最大目标ROI
    KInt _maxIndex;          // 最大轮廓ID                        //需根据模型信息设置合理的参数范围
    KInt _objectNumber;      // blob个数

    // 设置
    KBool _resizeToOrigin; // 是否变换为原尺寸                  //针对分割模型输出缩放等情况
    KInt _thresholdType;   // 阈值设置方式 0-不进行二值化（比如分割模型输出），1-单阈值，2-双阈值
    KInt _polarType;       // 极性设置，0-暗背景-灰度值高于阈值的为前景，1-亮背景-灰度值低于阈值的为前景
    KInt _lowThreshold;    // 低阈值
    // KInt _lowThreshold;             //高阈值
    KInt _segmentResultIndex; // 分割算子二值掩膜图类别-上一模块为ai分割时有效，从0开始
                              // 区域限制条件
    KInt _blobNumber;         // 查找最大个数，1个时找最大的
    KBool _enableLimitWidth;  // 最小宽度限制
    KInt _minWidth;           // 区域最小宽度
    KBool _enableLimitHeight; // 最小高度限制
    KInt _minHeight;          // 区域最小高度
    KBool _enableLimitArea;   // 最小面积限制
    KInt _minArea;            // 区域最小面积
};

#pragma endregion

#pragma region 圆查找模块
class KFindCircleModule : public KModule
{
public:
    KFindCircleModule();
    int run() override;

private:
    /*输入参数*/
    KImage2Data _image2In; // 输入图像
    KInt _minRadius = 0;   // 圆环内半径
    KInt _maxRadius = 0;   // 圆环外半径
    KInt _threshold = 0;   // 阈值
    KInt _minDist = 0;     // 查找圆间距

    /*输出参数*/
    KImage2Data _image2Out; // 结果图
};
#pragma endregion

#pragma region 椭圆查找模块
class KFindEllipseModule : public KModule
{
public:
    KFindEllipseModule();
    int run() override;

private:
    /*输入参数*/
    KImage2Data _image2In; // 输入图像
    KInt _minRadius = 0;   // 椭圆内半径
    KInt _maxRadius = 0;   // 椭圆外半径
    KInt _threshold = 0;   // 阈值
};
#pragma endregion

#pragma region 直线查找模块
class KFindLineModule : public KModule
{
public:
    KFindLineModule();
    int run() override;

private:
    // 输入
    KImage2Data _image2In; // 输入图像
    KRectData _roi;        // 查找ROI区域
    // cv::Rect _roi;              // 查找ROI区域
    KInt _edgePolarType = 0;     // 0-白到黑，1-黑到白
    KInt _binaryThreshold = 128; // 二值化阈值
    KInt _minPoints = 10;        // 拟合所需最少点数
    KInt _fitMethod = 1;         // 0-最小二乘，1-鲁棒Huber，2-ransac

    // 输出
    KImage2Data _image2Out; // 结果图
    KInt _ret = 0;          // 0-OK, 1-NG
    KFloat _angle;          // 直线角度，范围0-180
};
#pragma endregion

#pragma region 字符定位模块（深度学习）
class KCharPositionDLModule : public KModule
{
public:
    KCharPositionDLModule();
    int run() override;

private:
    // 输入
    KImage2Data _image2In; // 输入图像
    KSmartParamAIData _smartAi;

    // 输出
    KImage2Data _image2Out; // 结果图
    KRect1DData _rects;
};
#pragma endregion