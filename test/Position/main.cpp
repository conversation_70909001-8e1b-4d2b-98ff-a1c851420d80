﻿#include "KDataInclude.h"
#include "bmruntime_interface.h"
#include "aiEngine/KBitlandAIEngine.h"
#include "KCondition.h"
#include "algorithm/KAIEngine.h"
#include "KControllerAIEngineManager.h"
#include "module/Position/KPosition.h"

enum PositionType
{
    PositionType_Match,  // 模板匹配模块测试
    PositionType_Blob,   // Blob分析模块测试
    PositionType_Circle, // 圆查找模块测试
    PositionType_Line,   // 直线查找模块测试
};
int main(int argc, char *argv[])
{
    system("mkdir -p saveimages");
    system("rm -rf saveimages/*");
    PositionType positionType = PositionType_Circle;

    cv::Mat img = cv::imread("circle.png");
    if (img.empty())
    {
        std::cout << "Failed to load image" << std::endl;
        return -1;
    }
    std::cout << "img w = " << img.cols << " h = " << img.rows << std::endl;
    cv::cvtColor(img, img, cv::COLOR_BGR2RGB);
    KImage2Data image2In(img);

    auto start = std::chrono::high_resolution_clock::now();
    auto end = std::chrono::high_resolution_clock::now();
    std::chrono::duration<double, std::milli> elapsed;
    switch (positionType)
    {
    case PositionType_Match:
    {
        KInt downSampleNum, matchMethod;
        KDouble angleStep, minAngle, maxAngle;
        KBool rotateEnable;
        downSampleNum.setValue(0);
        matchMethod.setValue(5);
        angleStep.setValue(10);
        minAngle.setValue(-45.0);
        maxAngle.setValue(45.0);
        rotateEnable.setValue(1);

        KMatchModule mKMatchModule;
        mKMatchModule.setId("mKMatchModuleID");

        mKMatchModule.getParamData("downSampleNum")->bind(&downSampleNum);
        mKMatchModule.getParamData("matchMethod")->bind(&matchMethod);
        mKMatchModule.getParamData("angleStep")->bind(&angleStep);
        mKMatchModule.getParamData("minAngle")->bind(&minAngle);
        mKMatchModule.getParamData("maxAngle")->bind(&maxAngle);
        mKMatchModule.getParamData("rotateEnable")->bind(&rotateEnable);

        mKMatchModule.getParamData("image2In")->bind(&image2In);

        start = std::chrono::high_resolution_clock::now();
        mKMatchModule.run();
        end = std::chrono::high_resolution_clock::now();
        elapsed = end - start;
        std::cout << "模板匹配模块执行耗时: " << elapsed.count() << " 毫秒" << std::endl;

        KImage2Data image2Out;
        image2Out.bind(mKMatchModule.getResultData("image2Out"));
        cv::cvtColor(image2Out.Image2().src(), image2Out.Image2().src(), cv::COLOR_RGB2BGR);
        cv::imwrite("saveimages/result.jpg", image2Out.Image2().src());
    }
    break;
    case PositionType_Blob:
    {
    }
    break;
    case PositionType_Line:
    {
    }
    break;
    case PositionType_Circle:
    {
        KFindCircleModule mKFindCircleModule;
        mKFindCircleModule.setId("mKFindCircleModuleID");
        mKFindCircleModule.getParamData("image2In")->bind(&image2In);
        KInt minRadius, maxRadius, threshold, minDist;
        minRadius.setValue(25);
        maxRadius.setValue(75);
        threshold.setValue(100);
        minDist.setValue(30);

        mKFindCircleModule.getParamData("minRadius")->bind(&minRadius);
        mKFindCircleModule.getParamData("maxRadius")->bind(&maxRadius);
        mKFindCircleModule.getParamData("threshold")->bind(&threshold);
        mKFindCircleModule.getParamData("minDist")->bind(&minDist);

        start = std::chrono::high_resolution_clock::now();
        mKFindCircleModule.run();
        end = std::chrono::high_resolution_clock::now();
        elapsed = end - start;
        std::cout << "圆查找模块执行耗时: " << elapsed.count() << " 毫秒" << std::endl;
        KImage2Data image2Out;
        image2Out.bind(mKFindCircleModule.getResultData("image2Out"));
        cv::cvtColor(image2Out.Image2().src(), image2Out.Image2().src(), cv::COLOR_RGB2BGR);
        cv::imwrite("saveimages/result.jpg", image2Out.Image2().src());
    }
    break;
    default:
    {
    }
    break;
    }
    return 0;
}